/**
 * Language Manager Module
 * Handles language switching and translation functionality
 */

/**
 * Sets the language for the website and updates all translatable elements
 * @param {string} language - The language code (en, br, jp)
 */
function setLanguage(language) {
  // Check if translations object exists
  if (typeof translations === 'undefined') {
    console.error('Translations object not found. Make sure translations.js is loaded.');
    return;
  }

  // Check if the requested language exists
  if (!translations[language]) {
    console.warn(`Language '${language}' not found. Falling back to Brazilian Portuguese.`);
    language = 'br';
  }

  const elementsToTranslate = document.querySelectorAll("[data-translate]");
  elementsToTranslate.forEach((element) => {
    const translationKey = element.getAttribute("data-translate");
    const translation = translations[language][translationKey] || translationKey;
    element.innerHTML = translation;
  });

  // Save selected language to localStorage
  localStorage.setItem("selectedLanguage", language);

  // Update document language attribute
  document.documentElement.setAttribute("lang", language);

  // Update radio button selection
  updateLanguageRadioButton(language);
}

/**
 * Updates the radio button selection to match the current language
 * @param {string} language - The language code
 */
function updateLanguageRadioButton(language) {
  const radioButton = document.querySelector(
    `input[name="btnradio"][value="${language}"]`
  );
  if (radioButton) {
    radioButton.checked = true;
  }
}

/**
 * Gets the currently selected language from localStorage
 * @returns {string} - The current language code
 */
function getCurrentLanguage() {
  return localStorage.getItem("selectedLanguage") || "br";
}

/**
 * Initializes the language system with saved preferences
 */
function initializeLanguage() {
  const selectedLanguage = getCurrentLanguage();
  setLanguage(selectedLanguage);
}

/**
 * Sets up event listeners for language radio buttons
 */
function setupLanguageEventListeners() {
  document.querySelectorAll('input[name="btnradio"]').forEach((radio) => {
    radio.addEventListener("change", function () {
      if (this.checked) {
        setLanguage(this.value);
      }
    });
  });
}

/**
 * Gets available languages from the translations object
 * @returns {Array} - Array of available language codes
 */
function getAvailableLanguages() {
  if (typeof translations === 'undefined') {
    return ['br']; // Default fallback
  }
  return Object.keys(translations);
}

/**
 * Gets the translation for a specific key in the current language
 * @param {string} key - The translation key
 * @param {string} fallback - Fallback text if translation not found
 * @returns {string} - The translated text
 */
function getTranslation(key, fallback = key) {
  const currentLang = getCurrentLanguage();
  
  if (typeof translations === 'undefined' || !translations[currentLang]) {
    return fallback;
  }
  
  return translations[currentLang][key] || fallback;
}

// Export functions for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    setLanguage,
    getCurrentLanguage,
    initializeLanguage,
    setupLanguageEventListeners,
    getAvailableLanguages,
    getTranslation
  };
}
